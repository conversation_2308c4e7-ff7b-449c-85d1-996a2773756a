{"name": "voyadem-saas", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:voyadem-saas": "node dist/voyadem-saas/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/platform-server": "^19.0.0", "@angular/router": "^19.0.0", "@angular/ssr": "^19.0.6", "@emailjs/browser": "^4.4.1", "@omnedia/ngx-typewriter": "^2.0.0", "@primeng/themes": "^19.0.2", "express": "^4.18.2", "primeng": "^19.0.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "typed.js": "^2.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2"}}