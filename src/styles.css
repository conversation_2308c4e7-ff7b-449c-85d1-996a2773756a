@tailwind base;
@tailwind components;
@tailwind utilities;

/* Effet de bordure lumineux style Apple pour les features */
.feature-card-effect {
  position: relative;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  border: 1px solid rgba(255, 205, 0, 0.05);
}

.feature-card-effect::before {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 0.5rem;
  padding: 1px;
  background: linear-gradient(
    45deg,
    rgba(255, 205, 0, 0.3),
    rgba(255, 205, 0, 0.1),
    rgba(255, 205, 0, 0.3)
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.feature-card-effect:hover::before {
  opacity: 1;
}

/* <PERSON><PERSON><PERSON> l'effet existant pour les pricing cards */
.pricing-card-effect {
  @apply transition-all duration-700;
  transform: translateY(0);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-card-effect:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 30px -15px rgba(0, 0, 0, 0.07),
              0 5px 15px -5px rgba(0, 0, 0, 0.03);
}

/* Animations globales pour le composant social-proof */
@keyframes float-global {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 205, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 205, 0, 0.6);
  }
}

.animate-float-global {
  animation: float-global 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
