<header class="fixed w-full bg-white/80 backdrop-blur-sm z-50">
  <div class="max-w-7xl mx-auto">
    <nav class="flex items-center justify-between h-20 px-6">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <img src="assets/images/logo.png" alt="Voyadem" class="h-8">
      </div>

      <!-- Navigation principale -->
      <div class="hidden md:flex items-center space-x-8">
        <a href="#solutions" class="text-sm text-slate-600 hover:text-brand-500">Solutions</a>
        <a href="#features" class="text-sm text-slate-600 hover:text-brand-500">Fonctionnalités</a>
        <a href="#pricing" class="text-sm text-slate-600 hover:text-brand-500">Tarifs</a>
        <a href="#about" class="text-sm text-slate-600 hover:text-brand-500">À propos</a>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-4">
        <a href="/login" class="text-sm font-medium text-slate-700 hover:text-brand-500">
          Se connecter
        </a>
        <a href="#demo" class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-slate-900 bg-brand-500 rounded-lg shadow-sm hover:bg-brand-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2">
          Demander une démo
        </a>
      </div>

      <!-- Menu mobile -->
      <button type="button" class="md:hidden" (click)="toggleNav()">
        <span class="sr-only">Menu</span>
        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
        </svg>
      </button>
    </nav>
  </div>

  <!-- Menu mobile panel -->
  <div class="md:hidden" [class.hidden]="!isNavOpen">
    <div class="px-2 pt-2 pb-3 space-y-1">
      <a href="#solutions" class="block px-3 py-2 text-base font-medium text-slate-700 hover:bg-slate-50 rounded-md">Solutions</a>
      <a href="#features" class="block px-3 py-2 text-base font-medium text-slate-700 hover:bg-slate-50 rounded-md">Fonctionnalités</a>
      <a href="#pricing" class="block px-3 py-2 text-base font-medium text-slate-700 hover:bg-slate-50 rounded-md">Tarifs</a>
      <a href="#about" class="block px-3 py-2 text-base font-medium text-slate-700 hover:bg-slate-50 rounded-md">À propos</a>
    </div>
  </div>
</header>
