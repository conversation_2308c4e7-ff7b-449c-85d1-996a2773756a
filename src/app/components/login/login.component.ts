import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginData = {
    email: '',
    password: ''
  };

  isSubmitting = false;
  showError = false;
  errors: any = {};
  showPassword = false;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.initAnimations();
    }
  }

  private initAnimations(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    setTimeout(() => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  validateForm(): boolean {
    this.errors = {};
    
    if (!this.loginData.email.trim()) {
      this.errors.email = 'L\'email est requis';
    } else if (!this.isValidEmail(this.loginData.email)) {
      this.errors.email = 'Format d\'email invalide';
    }
    
    if (!this.loginData.password.trim()) {
      this.errors.password = 'Le mot de passe est requis';
    } else if (this.loginData.password.length < 6) {
      this.errors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  async onSubmit(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;
    this.showError = false;

    try {
      // Simuler la vérification de connexion
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Toujours afficher le message d'erreur stylé
      this.showError = true;
      
    } catch (error) {
      console.error('Erreur lors de la connexion:', error);
      this.showError = true;
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.loginData = {
      email: '',
      password: ''
    };
    this.errors = {};
    this.showError = false;
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      window.history.back();
    }
  }
}
