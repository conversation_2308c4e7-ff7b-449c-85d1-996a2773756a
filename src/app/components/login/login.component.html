<!-- Login Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-brand-50/30 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-0 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 4s;"></div>
  </div>

  <!-- Floating geometric shapes -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-20 left-10 w-4 h-4 bg-brand-400 rounded-full animate-float opacity-60"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-blue-400 rotate-45 animate-float-reverse opacity-40" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-20 w-3 h-3 bg-purple-400 rounded-full animate-float opacity-50" style="animation-delay: 3s;"></div>
    <div class="absolute bottom-20 right-40 w-5 h-5 bg-green-400 rotate-12 animate-float-reverse opacity-30" style="animation-delay: 2s;"></div>
  </div>

  <div class="relative">
    <!-- Header -->
    <div class="sm:mx-auto sm:w-full sm:max-w-md animate-on-scroll">
      <!-- Back button -->
      <button (click)="goBack()" 
              class="mb-8 inline-flex items-center text-sm font-medium text-slate-600 hover:text-slate-900 transition-colors group">
        <svg class="mr-2 w-4 h-4 transform group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Retour
      </button>

      <!-- Logo and brand -->
      <div class="text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-brand-500 to-blue-500 rounded-2xl shadow-lg mb-6">
          <span class="text-2xl font-bold text-white">V</span>
        </div>
        <h1 class="text-3xl font-bold text-slate-900 mb-2">
          Connexion à Voyadem
        </h1>
        <p class="text-base text-slate-600">
          Accédez à votre espace de gestion
        </p>
      </div>
    </div>

    <!-- Login Form -->
    <div class="mt-12 sm:mx-auto sm:w-full sm:max-w-md animate-on-scroll">
      <div class="relative">
        <!-- Glassmorphism background -->
        <div class="absolute inset-0 bg-white/60 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50"></div>
        
        <div class="relative py-12 px-8 sm:px-12">
          <!-- Error Message -->
          <div *ngIf="showError" 
               class="mb-6 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-xl animate-shake">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Agence non trouvée
                </h3>
                <p class="mt-1 text-sm text-red-700">
                  Cette agence n'est pas encore enregistrée dans notre base de données. Contactez notre équipe pour créer votre compte.
                </p>
              </div>
            </div>
          </div>

          <form (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                Email professionnel
              </label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="loginData.email"
                [class]="'form-input ' + (errors.email ? 'error' : '')"
                placeholder="<EMAIL>"
                autocomplete="email"
                required>
              <div *ngIf="errors.email" class="error-message">{{ errors.email }}</div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
              <label for="password" class="block text-sm font-medium text-slate-700 mb-2">
                Mot de passe
              </label>
              <div class="relative">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  id="password"
                  name="password"
                  [(ngModel)]="loginData.password"
                  [class]="'form-input pr-12 ' + (errors.password ? 'error' : '')"
                  placeholder="••••••••"
                  autocomplete="current-password"
                  required>
                <button
                  type="button"
                  (click)="togglePasswordVisibility()"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-slate-600 transition-colors">
                  <svg *ngIf="!showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  <svg *ngIf="showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                  </svg>
                </button>
              </div>
              <div *ngIf="errors.password" class="error-message">{{ errors.password }}</div>
            </div>

            <!-- Remember me & Forgot password -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  class="h-4 w-4 text-brand-600 focus:ring-brand-500 border-slate-300 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-slate-700">
                  Se souvenir de moi
                </label>
              </div>

              <div class="text-sm">
                <a href="#" class="font-medium text-brand-600 hover:text-brand-500 transition-colors">
                  Mot de passe oublié ?
                </a>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-2">
              <button
                type="submit"
                [disabled]="isSubmitting"
                class="w-full group relative inline-flex items-center justify-center px-8 py-4 text-base font-medium text-slate-900 bg-white rounded-xl shadow-sm hover:shadow-md border border-slate-200 hover:border-slate-300 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden">
                
                <!-- Gradient overlay on hover -->
                <span class="absolute inset-0 bg-gradient-to-r from-brand-500 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                
                <span class="relative flex items-center font-semibold text-slate-900 group-hover:text-slate-900 transition-colors duration-300">
                  <span *ngIf="!isSubmitting">Se connecter</span>
                  <span *ngIf="isSubmitting" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Connexion...
                  </span>
                  
                  <svg *ngIf="!isSubmitting" class="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  </svg>
                </span>
              </button>
            </div>
          </form>

          <!-- Footer -->
          <div class="mt-8 text-center">
            <p class="text-sm text-slate-600">
              Pas encore client ? 
              <a href="#demo" class="font-medium text-brand-600 hover:text-brand-500 transition-colors">
                Demander une démo
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
