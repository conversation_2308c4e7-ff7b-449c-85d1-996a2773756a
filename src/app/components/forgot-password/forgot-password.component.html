<!-- Forgot Password Page -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-brand-50/30 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-400/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-0 w-64 h-64 bg-brand-300/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 4s;"></div>
  </div>

  <!-- Floating geometric shapes -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-20 left-10 w-4 h-4 bg-brand-400 rounded-full animate-float opacity-60"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-brand-500 rotate-45 animate-float-reverse opacity-40" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-20 w-3 h-3 bg-brand-300 rounded-full animate-float opacity-50" style="animation-delay: 3s;"></div>
    <div class="absolute bottom-20 right-40 w-5 h-5 bg-brand-600 rotate-12 animate-float-reverse opacity-30" style="animation-delay: 2s;"></div>
  </div>

  <div class="relative">
    <!-- Header -->
    <div class="sm:mx-auto sm:w-full sm:max-w-md animate-on-scroll">
      <!-- Back button -->
      <button (click)="goBack()" 
              class="mb-8 inline-flex items-center text-sm font-medium text-slate-600 hover:text-slate-900 transition-colors group">
        <svg class="mr-2 w-4 h-4 transform group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Retour
      </button>

      <!-- Logo and brand -->
      <div class="text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-2xl shadow-lg mb-6 border border-slate-200">
          <img src="/assets/images/logo.png" alt="Voyadem" class="h-10 w-auto">
        </div>
        <h1 class="text-3xl font-bold text-slate-900 mb-2">
          Mot de passe oublié
        </h1>
        <p class="text-base text-slate-600">
          Saisissez votre email pour recevoir un lien de réinitialisation
        </p>
      </div>
    </div>

    <!-- Form Container -->
    <div class="mt-12 sm:mx-auto sm:w-full sm:max-w-md animate-on-scroll">
      <div class="relative">
        <!-- Glassmorphism background -->
        <div class="absolute inset-0 bg-white/60 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50"></div>
        
        <div class="relative py-12 px-8 sm:px-12">
          <!-- Error Message -->
          <div *ngIf="showError && !isSubmitted"
               class="mb-6 p-4 bg-red-50/80 backdrop-blur-sm border border-red-200 rounded-xl animate-shake">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Email non trouvé
                </h3>
                <p class="mt-1 text-sm text-red-700">
                  Cette adresse email n'est pas associée à un compte dans notre base de données. Vérifiez l'adresse ou contactez notre équipe.
                </p>
              </div>
            </div>
          </div>

          <!-- Success State -->
          <div *ngIf="isSubmitted" class="text-center py-8">
            <div class="w-16 h-16 bg-brand-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-slate-900 mb-4">Email envoyé !</h3>
            <p class="text-slate-600 mb-8">
              Si cette adresse email est associée à un compte, vous recevrez un lien de réinitialisation dans quelques minutes.
            </p>
            <div class="space-y-4">
              <button (click)="resetForm()" 
                      class="w-full inline-flex items-center justify-center px-6 py-3 text-base font-medium text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors">
                Renvoyer l'email
              </button>
              <a href="/login" 
                 class="block text-center text-sm font-medium text-brand-600 hover:text-brand-500 transition-colors">
                Retour à la connexion
              </a>
            </div>
          </div>

          <!-- Form -->
          <form *ngIf="!isSubmitted" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                Email professionnel
              </label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="email"
                [class]="'form-input ' + (errors.email ? 'error' : '')"
                placeholder="<EMAIL>"
                autocomplete="email"
                required>
              <div *ngIf="errors.email" class="error-message">{{ errors.email }}</div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <button
                type="submit"
                [disabled]="isSubmitting"
                class="w-full group relative inline-flex items-center justify-center px-8 py-4 text-base font-medium text-slate-900 bg-white rounded-xl shadow-sm hover:shadow-md border border-slate-200 hover:border-slate-300 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden">
                
                <!-- Gradient overlay on hover -->
                <span class="absolute inset-0 bg-gradient-to-r from-brand-500 to-brand-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                
                <span class="relative flex items-center font-semibold text-slate-900 group-hover:text-slate-900 transition-colors duration-300">
                  <span *ngIf="!isSubmitting">Envoyer le lien</span>
                  <span *ngIf="isSubmitting" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Envoi en cours...
                  </span>
                  
                  <svg *ngIf="!isSubmitting" class="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  </svg>
                </span>
              </button>
            </div>

            <!-- Footer -->
            <div class="text-center pt-4">
              <p class="text-sm text-slate-600">
                Vous vous souvenez de votre mot de passe ? 
                <a href="/login" class="font-medium text-brand-600 hover:text-brand-500 transition-colors">
                  Se connecter
                </a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
