import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {
  email = '';
  isSubmitting = false;
  isSubmitted = false;
  errors: any = {};

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.initAnimations();
    }
  }

  private initAnimations(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    setTimeout(() => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  validateForm(): boolean {
    this.errors = {};
    
    if (!this.email.trim()) {
      this.errors.email = 'L\'email est requis';
    } else if (!this.isValidEmail(this.email)) {
      this.errors.email = 'Format d\'email invalide';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async onSubmit(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // Simuler l'envoi de l'email de réinitialisation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.isSubmitted = true;
      
    } catch (error) {
      console.error('Erreur lors de l\'envoi:', error);
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.email = '';
    this.isSubmitted = false;
    this.errors = {};
  }

  goBack(): void {
    if (isPlatformBrowser(this.platformId)) {
      window.history.back();
    }
  }
}
