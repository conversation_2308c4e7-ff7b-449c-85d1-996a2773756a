/* Forgot Password Form Styles Apple-like */
.form-input {
  @apply w-full px-4 py-3 text-base text-slate-900 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl shadow-sm transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:border-brand-500;
  @apply placeholder:text-slate-400;
}

.form-input:hover {
  @apply border-slate-300 shadow-md;
}

.form-input.error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500/20;
}

.form-group {
  @apply relative;
}

.error-message {
  @apply text-sm text-red-600 mt-1 ml-1;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Animation classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 4s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Custom focus styles */
.form-input:focus {
  transform: translateY(-1px);
}

/* Button hover effects */
button[type="submit"]:hover:not(:disabled) {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Glassmorphism enhancements */
.form-input {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Brand logo animation */
.inline-flex.items-center.justify-center.w-16.h-16 {
  transition: all 0.3s ease;
}

.inline-flex.items-center.justify-center.w-16.h-16:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 10px 25px -5px rgba(255, 205, 0, 0.3);
}

/* Form container entrance animation */
.relative.py-12.px-8 {
  animation: slideInUp 0.8s ease-out 0.2s both;
}

/* Success icon animation */
@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-icon {
  animation: checkmark 0.6s ease-out;
}

/* Prevent eye icon from moving on hover */
button[type="button"] svg {
  transition: color 0.2s ease;
  transform: none !important;
}

button[type="button"]:hover svg {
  transform: none !important;
}

/* Back button hover effect */
button.group:hover svg {
  transform: translateX(-4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-input {
    @apply text-base; /* Prevent zoom on iOS */
  }

  .animate-on-scroll {
    transform: translateY(20px);
  }
}

/* Input autofill styles */
.form-input:-webkit-autofill,
.form-input:-webkit-autofill:hover,
.form-input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.8) inset;
  -webkit-text-fill-color: #1e293b;
  transition: background-color 5000s ease-in-out 0s;
}

/* Link hover effects */
a {
  position: relative;
  overflow: hidden;
}

a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #ffcd00;
  transition: width 0.3s ease;
}

a:hover::before {
  width: 100%;
}
