<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h2 class="text-base font-medium tracking-tight text-brand-500 uppercase">Notre Impact</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl">
        Des chiffres qui parlent
      </p>
    </div>

    <div class="mt-20 grid grid-cols-1 gap-8 lg:grid-cols-3">
      <!-- Statistique 1 -->
      <div class="relative">
        <div class="absolute -inset-1 rounded-2xl bg-gradient-to-r from-brand-400 to-brand-600 opacity-10"></div>
        <div class="relative p-6 bg-white rounded-xl shadow-sm text-center">
          <div class="text-4xl font-bold text-brand-500 mb-2">98%</div>
          <h4 class="text-lg font-medium text-slate-900">Satisfaction Client</h4>
          <p class="mt-2 text-slate-600">
            De nos utilisateurs recommandent Voyadem à leurs collègues
          </p>
        </div>
      </div>

      <!-- Statistique 2 -->
      <div class="relative">
        <div class="absolute -inset-1 rounded-2xl bg-gradient-to-r from-brand-400 to-brand-600 opacity-10"></div>
        <div class="relative p-6 bg-white rounded-xl shadow-sm text-center">
          <div class="text-4xl font-bold text-brand-500 mb-2">3h</div>
          <h4 class="text-lg font-medium text-slate-900">Gain de Temps</h4>
          <p class="mt-2 text-slate-600">
            En moyenne par jour sur les tâches administratives
          </p>
        </div>
      </div>

      <!-- Statistique 3 -->
      <div class="relative">
        <div class="absolute -inset-1 rounded-2xl bg-gradient-to-r from-brand-400 to-brand-600 opacity-10"></div>
        <div class="relative p-6 bg-white rounded-xl shadow-sm text-center">
          <div class="text-4xl font-bold text-brand-500 mb-2">+40%</div>
          <h4 class="text-lg font-medium text-slate-900">Productivité</h4>
          <p class="mt-2 text-slate-600">
            D'augmentation moyenne de la productivité des agences
          </p>
        </div>
      </div>
    </div>

    <div class="mt-16 text-center">
      <a href="/demo" class="inline-flex items-center justify-center px-6 py-3 text-base font-medium text-slate-900 bg-brand-500 rounded-lg shadow-sm hover:bg-brand-400">
        Découvrez comment par vous-même
        <svg class="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
        </svg>
      </a>
    </div>
  </div>
</section>
