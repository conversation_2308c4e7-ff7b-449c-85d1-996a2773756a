<!-- Contact Section -->
<section id="contact" class="relative py-24 bg-gradient-to-br from-slate-50 via-white to-brand-50/30 overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-brand-400/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-0 w-64 h-64 bg-brand-300/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 4s;"></div>
  </div>

  <!-- Floating geometric shapes -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-20 left-10 w-4 h-4 bg-brand-400 rounded-full animate-float opacity-60"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-brand-500 rotate-45 animate-float-reverse opacity-40" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-20 w-3 h-3 bg-brand-300 rounded-full animate-float opacity-50" style="animation-delay: 3s;"></div>
    <div class="absolute bottom-20 right-40 w-5 h-5 bg-brand-600 rotate-12 animate-float-reverse opacity-30" style="animation-delay: 2s;"></div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16 animate-on-scroll">
      <div class="inline-flex items-center justify-center p-2 bg-brand-100 rounded-full mb-6">
        <div class="flex items-center space-x-1">
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.2s;"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.4s;"></div>
        </div>
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
        Contactez-nous
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto">
        Notre équipe commerciale est là pour répondre à toutes vos questions et vous accompagner dans votre projet
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
      <!-- Contact Information -->
      <div class="animate-on-scroll">
        <h3 class="text-2xl font-bold text-slate-900 mb-8">Nos coordonnées</h3>

        <div class="bg-white/50 backdrop-blur-sm rounded-2xl p-6 border border-white/50 shadow-sm">
          <div class="space-y-5">
            <div *ngFor="let info of contactInfo; let i = index"
                 class="flex items-start space-x-4"
                 [style.animation-delay]="(i * 0.1) + 's'">

              <!-- Icon -->
              <div class="flex-shrink-0 w-10 h-10 bg-brand-100 rounded-lg flex items-center justify-center">
                <!-- Phone Icon -->
                <svg *ngIf="info.icon === 'phone'" class="w-5 h-5 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>

                <!-- Email Icon -->
                <svg *ngIf="info.icon === 'email'" class="w-5 h-5 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>

                <!-- Location Icon -->
                <svg *ngIf="info.icon === 'location'" class="w-5 h-5 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
              </div>

              <!-- Content -->
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-slate-900 mb-1">{{ info.title }}</h4>
                <p class="text-brand-600 font-medium mb-1">{{ info.value }}</p>
                <p class="text-sm text-slate-500">{{ info.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="mt-12 p-6 bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-2xl border border-brand-200/50">
          <h3 class="text-lg font-semibold text-slate-900 mb-3">Pourquoi nous choisir ?</h3>
          <ul class="space-y-2 text-sm text-slate-600">
            <li class="flex items-center">
              <svg class="w-4 h-4 text-brand-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              Réponse garantie sous 24h
            </li>
            <li class="flex items-center">
              <svg class="w-4 h-4 text-brand-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              Accompagnement personnalisé
            </li>
            <li class="flex items-center">
              <svg class="w-4 h-4 text-brand-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              Devis gratuit et sans engagement
            </li>
          </ul>
        </div>
      </div>

      <!-- Contact Form -->
      <div class="animate-on-scroll">
        <div class="relative">
          <!-- Glassmorphism background -->
          <div class="absolute inset-0 bg-white/60 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50"></div>
          
          <div class="relative p-8 md:p-12">
            <!-- Success State -->
            <div *ngIf="isSubmitted" class="text-center py-12">
              <div class="w-16 h-16 bg-brand-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-8 h-8 text-brand-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
              </div>
              <h3 class="text-2xl font-semibold text-slate-900 mb-4">Message envoyé !</h3>
              <p class="text-slate-600 mb-8">Merci pour votre message. Notre équipe vous contactera dans les plus brefs délais.</p>
              <button (click)="resetForm()" 
                      class="inline-flex items-center px-6 py-3 text-base font-medium text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors">
                Envoyer un autre message
              </button>
            </div>

            <!-- Form -->
            <form *ngIf="!isSubmitted" (ngSubmit)="onSubmit()" class="space-y-6">
              <!-- Name & Email Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name Field -->
                <div class="form-group">
                  <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                    Nom complet *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    [(ngModel)]="contactData.name"
                    [class]="'form-input ' + (errors.name ? 'error' : '')"
                    placeholder="Votre nom"
                    required>
                  <div *ngIf="errors.name" class="error-message">{{ errors.name }}</div>
                </div>

                <!-- Email Field -->
                <div class="form-group">
                  <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                    Email professionnel *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    [(ngModel)]="contactData.email"
                    [class]="'form-input ' + (errors.email ? 'error' : '')"
                    placeholder="<EMAIL>"
                    required>
                  <div *ngIf="errors.email" class="error-message">{{ errors.email }}</div>
                </div>
              </div>

              <!-- Company & Phone Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Company Field -->
                <div class="form-group">
                  <label for="company" class="block text-sm font-medium text-slate-700 mb-2">
                    Nom de l'agence *
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    [(ngModel)]="contactData.company"
                    [class]="'form-input ' + (errors.company ? 'error' : '')"
                    placeholder="Nom de votre agence"
                    required>
                  <div *ngIf="errors.company" class="error-message">{{ errors.company }}</div>
                </div>

                <!-- Phone Field -->
                <div class="form-group">
                  <label for="phone" class="block text-sm font-medium text-slate-700 mb-2">
                    Téléphone *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    [(ngModel)]="contactData.phone"
                    [class]="'form-input ' + (errors.phone ? 'error' : '')"
                    placeholder="+212 6 12 34 56 78"
                    required>
                  <div *ngIf="errors.phone" class="error-message">{{ errors.phone }}</div>
                </div>
              </div>

              <!-- Subject Field -->
              <div class="form-group">
                <label for="subject" class="block text-sm font-medium text-slate-700 mb-2">
                  Sujet de votre demande
                </label>
                <select
                  id="subject"
                  name="subject"
                  [(ngModel)]="contactData.subject"
                  class="form-input">
                  <option *ngFor="let subject of subjects" [value]="subject.value">
                    {{ subject.label }}
                  </option>
                </select>
              </div>

              <!-- Message Field -->
              <div class="form-group">
                <label for="message" class="block text-sm font-medium text-slate-700 mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  [(ngModel)]="contactData.message"
                  rows="5"
                  [class]="'form-input resize-none ' + (errors.message ? 'error' : '')"
                  placeholder="Décrivez votre projet et vos besoins..."
                  required></textarea>
                <div *ngIf="errors.message" class="error-message">{{ errors.message }}</div>
              </div>

              <!-- Submit Button -->
              <div class="pt-4">
                <button
                  type="submit"
                  [disabled]="isSubmitting"
                  class="w-full group relative inline-flex items-center justify-center px-8 py-4 text-base font-medium text-slate-900 bg-white rounded-xl shadow-sm hover:shadow-md border border-slate-200 hover:border-slate-300 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden">
                  
                  <!-- Gradient overlay on hover -->
                  <span class="absolute inset-0 bg-gradient-to-r from-brand-500 to-brand-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  
                  <span class="relative flex items-center font-semibold text-slate-900 group-hover:text-slate-900 transition-colors duration-300">
                    <span *ngIf="!isSubmitting">Envoyer le message</span>
                    <span *ngIf="isSubmitting" class="flex items-center">
                      <svg class="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Envoi en cours...
                    </span>
                    
                    <svg *ngIf="!isSubmitting" class="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
