import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmailService } from '../../services/email.service';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.css']
})
export class ContactComponent implements OnInit {
  contactData = {
    name: '',
    email: '',
    company: '',
    phone: '',
    subject: 'commercial',
    message: ''
  };

  isSubmitting = false;
  isSubmitted = false;
  errors: any = {};

  subjects = [
    { value: 'commercial', label: 'Demande commerciale' },
    { value: 'support', label: 'Support technique' },
    { value: 'partnership', label: 'Partenariat' },
    { value: 'other', label: 'Autre demande' }
  ];

  contactInfo = [
    {
      icon: 'phone',
      title: 'Téléphone',
      value: '+*********** 517',
      description: 'Disponible 7j/7'
    },
    {
      icon: 'email',
      title: 'Email',
      value: '<EMAIL>',
      description: 'Réponse sous 24h'
    },
    {
      icon: 'location',
      title: 'Adresse',
      value: 'Casablanca, Maroc',
      description: 'Siège social'
    }
  ];

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private emailService: EmailService
  ) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.initAnimations();
    }
  }

  private initAnimations(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    setTimeout(() => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  validateForm(): boolean {
    this.errors = {};
    
    if (!this.contactData.name.trim()) {
      this.errors.name = 'Le nom est requis';
    }
    
    if (!this.contactData.email.trim()) {
      this.errors.email = 'L\'email est requis';
    } else if (!this.isValidEmail(this.contactData.email)) {
      this.errors.email = 'Email invalide';
    }
    
    if (!this.contactData.company.trim()) {
      this.errors.company = 'Le nom de l\'entreprise est requis';
    }

    if (!this.contactData.phone.trim()) {
      this.errors.phone = 'Le téléphone est requis';
    }

    if (!this.contactData.message.trim()) {
      this.errors.message = 'Le message est requis';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async onSubmit(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // Envoyer via EmailJS
      await this.emailService.sendContactMessage(this.contactData);

      console.log('Message de contact envoyé:', this.contactData);
      this.isSubmitted = true;

      // Reset form after success
      setTimeout(() => {
        this.resetForm();
      }, 4000);

    } catch (error) {
      console.error('Erreur lors de l\'envoi:', error);
      // Vous pouvez ajouter une gestion d'erreur ici
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.contactData = {
      name: '',
      email: '',
      company: '',
      phone: '',
      subject: 'commercial',
      message: ''
    };
    this.isSubmitted = false;
    this.errors = {};
  }
}
