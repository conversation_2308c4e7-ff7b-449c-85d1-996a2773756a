import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import Typed from 'typed.js';

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.css']
})
export class HeroComponent implements OnInit {
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.initTyped();
    }
  }

  private initTyped() {
    const options = {
      strings: ['Gérez votre agence^500\n<span class="text-brand-500">en toute simplicité</span>'],
      typeSpeed: 40,
      backSpeed: 25,
      showCursor: true,
      cursorChar: '|',
      loop: false,
      startDelay: 300,
      preStringTyped: (pos: number) => {
        const el = document.querySelector('.typed-element');
        if (el) el.innerHTML = el.innerHTML.replace(/\n/g, '<br>');
      },
      onStringTyped: (pos: number) => {
        const cursor = document.querySelector('.typed-cursor');
        if (cursor) cursor.classList.add('text-brand-500');
      }
    };

    const typed = new Typed('.typed-element', options);
  }
}
