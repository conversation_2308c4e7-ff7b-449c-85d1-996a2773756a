<section class="relative bg-white min-h-screen flex flex-col justify-center overflow-hidden pt-20 md:pt-0">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
    <!-- Text Content -->
    <div class="text-center max-w-3xl mx-auto">
      <h1 class="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl md:text-6xl">
        <span class="typed-element"></span>
      </h1>
      <p class="mt-6 text-lg text-slate-600">
        Voyadem révolutionne la gestion des agences de voyages avec une solution tout-en-un.
        Simplifiez vos opérations quotidiennes et concentrez-vous sur l'essentiel.
      </p>
      <div class="mt-6 mb-8 flex flex-col sm:flex-row justify-center gap-4">
        <a href="#demo" class="rounded-lg bg-brand-500 px-6 py-3 text-base font-medium text-slate-900 shadow-sm hover:bg-brand-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 text-center">
          Demander une démo
        </a>
        <a href="#features" class="rounded-lg bg-slate-50 px-6 py-3 text-base font-medium text-slate-900 hover:bg-slate-100 text-center">
          En savoir plus
        </a>
      </div>
    </div>

    <!-- Dashboard Preview -->
    <div class="mt-28 max-w-5xl mx-auto">
      <div class="relative">
        <!-- Background decorative elements -->
        <div class="absolute -inset-x-20 -top-20 -bottom-20 bg-brand-50/50 rounded-[40px] rotate-6 transform"></div>
        <div class="absolute -inset-x-20 -top-20 -bottom-20 bg-brand-100/30 rounded-[40px] -rotate-3 transform"></div>

        <!-- Main dashboard container -->
        <div class="relative rounded-2xl overflow-hidden shadow-2xl backdrop-blur-sm bg-white/90 border border-white/20">
          <!-- Browser-like top bar -->
          <div class="flex items-center gap-2 px-4 py-3 bg-slate-50/80 border-b border-slate-200/50">
            <div class="flex gap-1.5">
              <div class="w-3 h-3 rounded-full bg-red-400"></div>
              <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
              <div class="w-3 h-3 rounded-full bg-green-400"></div>
            </div>
          </div>

          <!-- Dashboard content -->
          <div class="relative">
            <img src="assets/images/dashboard-preview.png"
                 alt="Dashboard Voyadem"
                 class="w-full transform transition-transform hover:scale-102 duration-500">
            <div class="absolute inset-0 bg-gradient-to-t from-white/10 via-transparent to-transparent"></div>
          </div>
        </div>

        <!-- Floating elements -->
        <div class="absolute -right-6 top-10 bg-white rounded-lg shadow-lg p-2 md:p-4 scale-75 md:scale-100 animate-float-slow">
          <div class="flex items-center gap-2 md:gap-3">
            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-brand-500/20 flex items-center justify-center">
              <svg class="w-4 h-4 md:w-5 md:h-5 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
              </svg>
            </div>
            <div>
              <p class="text-xs md:text-sm font-medium text-slate-900">Clients</p>
              <p class="text-[10px] md:text-xs text-slate-500">2.3K actifs</p>
            </div>
          </div>
        </div>

        <div class="absolute -left-6 md:top-32 top-10 bg-white rounded-lg shadow-lg p-2 md:p-4 scale-75 md:scale-100 animate-float">
          <div class="flex items-center gap-2 md:gap-3">
            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
              <svg class="w-4 h-4 md:w-5 md:h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
            </div>
            <div>
              <p class="text-xs md:text-sm font-medium text-slate-900">Hébergement</p>
              <p class="text-[10px] md:text-xs text-slate-500">+156 partenaires</p>
            </div>
          </div>
        </div>

        <div class="absolute -left-6 bottom-10 bg-white rounded-lg shadow-lg p-2 md:p-4 scale-75 md:scale-100 animate-float">
          <div class="flex items-center gap-2 md:gap-3">
            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-green-500/20 flex items-center justify-center">
              <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div>
              <p class="text-xs md:text-sm font-medium text-slate-900">Compagnies aériennes</p>
              <p class="text-[10px] md:text-xs text-slate-500">+45 partenaires</p>
            </div>
          </div>
        </div>

        <div class="absolute -right-6 bottom-10 bg-white rounded-lg shadow-lg p-2 md:p-4 scale-75 md:scale-100 animate-float">
          <div class="flex items-center gap-2 md:gap-3">
            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
              <svg class="w-4 h-4 md:w-5 md:h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
            </div>
            <div>
              <p class="text-xs md:text-sm font-medium text-slate-900">Programmes</p>
              <p class="text-[10px] md:text-xs text-slate-500">45 en cours</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</section>
