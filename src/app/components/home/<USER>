import {Component} from '@angular/core';
import {HeaderComponent} from '../header/header.component';
import {HeroComponent} from '../hero/hero.component';
import {SocialProofComponent} from '../social-proof/social-proof.component';
import {FeaturesComponent} from '../features/features.component';
import {PricingComponent} from '../pricing/pricing.component';
import {TestimonialsComponent} from '../testimonials/testimonials.component';
import {DemoFormComponent} from '../demo-form/demo-form.component';
import {FooterComponent} from '../footer/footer.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [HeaderComponent, HeroComponent, SocialProofComponent, FeaturesComponent, PricingComponent, TestimonialsComponent, DemoFormComponent, FooterComponent],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent {

}
