<!-- Social Proof Section -->
<section class="relative py-24 bg-gradient-to-br from-slate-50 via-white to-brand-50/30 overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-1/4 w-96 h-96 bg-brand-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-0 right-1/4 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-0 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 4s;"></div>
  </div>

  <!-- Floating geometric shapes -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-20 left-10 w-4 h-4 bg-brand-400 rounded-full animate-float opacity-60"></div>
    <div class="absolute top-40 right-20 w-6 h-6 bg-blue-400 rotate-45 animate-float-reverse opacity-40" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-20 w-3 h-3 bg-purple-400 rounded-full animate-float opacity-50" style="animation-delay: 3s;"></div>
    <div class="absolute bottom-20 right-40 w-5 h-5 bg-green-400 rotate-12 animate-float-reverse opacity-30" style="animation-delay: 2s;"></div>
  </div>

  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16 animate-on-scroll">
      <div class="inline-flex items-center justify-center p-2 bg-brand-100 rounded-full mb-4">
        <div class="flex items-center space-x-1">
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.2s;"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.4s;"></div>
        </div>
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
        <span class="bg-gradient-to-r from-slate-900 via-brand-600 to-slate-900 bg-clip-text text-transparent">
          Ils nous font confiance
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto">
        Rejoignez les agences de voyage qui ont déjà révolutionné leur gestion avec Voyadem
      </p>
    </div>



    <!-- Clients Grid -->
    <div class="animate-on-scroll">
      <div class="relative">
        <!-- Gradient borders -->
        <div class="absolute inset-0 bg-gradient-to-r from-brand-500 via-blue-500 to-purple-500 rounded-3xl blur-sm opacity-20"></div>
        
        <div class="relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/50 shadow-2xl">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center justify-center max-w-2xl mx-auto">
            <div *ngFor="let client of clients; let i = index" 
                 class="group relative transform transition-all duration-500 hover:scale-110"
                 [style.animation-delay]="(i * 0.1) + 's'">
              
              <!-- Hover effect background -->
              <div class="absolute inset-0 bg-gradient-to-r from-brand-500/10 to-blue-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm"></div>
              
              <a [href]="client.url" 
                 target="_blank" 
                 rel="noopener noreferrer"
                 class="relative block p-4 rounded-xl transition-all duration-300 group-hover:bg-white/50">
                
                <!-- Logos des clients -->
                <div class="flex flex-col items-center space-y-4">
                  <div class="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl flex items-center justify-center group-hover:from-brand-50 group-hover:to-blue-50 transition-all duration-300 p-3">
                    <img [src]="client.logo"
                         [alt]="client.name + ' logo'"
                         class="max-h-12 max-w-12 w-auto h-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300">
                  </div>

                  <div class="text-center">
                    <div class="text-lg font-semibold text-slate-900 group-hover:text-brand-600 transition-colors">
                      {{ client.name }}
                    </div>
                    <div class="text-sm text-slate-600 group-hover:text-slate-700 transition-colors">
                      {{ client.description }}
                    </div>
                  </div>
                </div>

                <!-- Hover tooltip -->
                <div class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-slate-800 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-10">
                  {{ client.description }}
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-800"></div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mt-16 animate-on-scroll">
      <div class="inline-flex flex-col sm:flex-row items-center gap-4">
        <p class="text-lg font-medium text-slate-700">
          Prêt à rejoindre nos partenaires ?
        </p>
        <a href="/demo"
           class="group relative inline-flex items-center justify-center px-8 py-3 text-base font-medium text-slate-900 bg-gradient-to-r from-brand-500 to-blue-500 rounded-xl shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-105 overflow-hidden">
          <span class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
          <span class="relative flex items-center font-semibold">
            Demander une démo
            <svg class="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
            </svg>
          </span>
        </a>
      </div>
    </div>
  </div>
</section>
