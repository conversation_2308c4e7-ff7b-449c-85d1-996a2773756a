import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-social-proof',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './social-proof.component.html',
  styleUrls: ['./social-proof.component.css']
})
export class SocialProofComponent implements OnInit {
  clients = [
    {
      name: 'Orastic',
      logo: 'assets/logos/client1.png',
      url: 'https://orastic.com',
      description: 'Solution digitale innovante'
    },
    {
      name: 'Lunatem',
      logo: 'assets/logos/lunatem.svg',
      url: 'https://lunatem.com',
      description: 'Agence de voyage premium'
    }
  ];

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.initAnimations();
    }
  }

  private initAnimations(): void {
    // Animation d'apparition progressive des éléments
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    // Observer tous les éléments avec la classe 'animate-on-scroll'
    setTimeout(() => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }, 100);
  }
}
