/* Form Styles Apple-like */
.form-input {
  @apply w-full px-4 py-3 text-base text-slate-900 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl shadow-sm transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:border-brand-500;
  @apply placeholder:text-slate-400;
}

.form-input:hover {
  @apply border-slate-300 shadow-md;
}

.form-input.error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500/20;
}

.form-group {
  @apply relative;
}

.error-message {
  @apply text-sm text-red-600 mt-1 ml-1;
}

/* Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Custom focus styles */
.form-input:focus {
  transform: translateY(-1px);
}

/* Floating label effect */
.form-group label {
  transition: all 0.2s ease;
}

.form-input:focus + label,
.form-input:not(:placeholder-shown) + label {
  transform: translateY(-8px) scale(0.85);
  color: #3b82f6;
}

/* Button hover effects */
button[type="submit"]:hover:not(:disabled) {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Success animation */
@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-checkmark {
  animation: checkmark 0.6s ease-out;
}

/* Glassmorphism enhancements */
.form-input {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-input {
    @apply text-base; /* Prevent zoom on iOS */
  }
}

/* Custom scrollbar for textarea */
textarea.form-input::-webkit-scrollbar {
  width: 6px;
}

textarea.form-input::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

textarea.form-input::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

textarea.form-input::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Input autofill styles */
.form-input:-webkit-autofill,
.form-input:-webkit-autofill:hover,
.form-input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.8) inset;
  -webkit-text-fill-color: #1e293b;
  transition: background-color 5000s ease-in-out 0s;
}

/* Subtle animations for form elements */
.form-group {
  animation: slideInUp 0.6s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button gradient overlay */
button[type="submit"] {
  position: relative;
  overflow: hidden;
}

button[type="submit"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

button[type="submit"]:hover::before {
  left: 100%;
}
