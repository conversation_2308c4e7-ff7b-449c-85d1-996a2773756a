import { Component, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmailService } from '../../services/email.service';

@Component({
  selector: 'app-demo-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './demo-form.component.html',
  styleUrls: ['./demo-form.component.css']
})
export class DemoFormComponent implements OnInit {
  formData = {
    name: '',
    email: '',
    company: '',
    phone: '',
    message: ''
  };

  isSubmitting = false;
  isSubmitted = false;
  errors: any = {};

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private emailService: EmailService
  ) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.initAnimations();
    }
  }

  private initAnimations(): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, { threshold: 0.1 });

    setTimeout(() => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach((el) => observer.observe(el));
    }, 100);
  }

  validateForm(): boolean {
    this.errors = {};
    
    if (!this.formData.name.trim()) {
      this.errors.name = 'Le nom est requis';
    }
    
    if (!this.formData.email.trim()) {
      this.errors.email = 'L\'email est requis';
    } else if (!this.isValidEmail(this.formData.email)) {
      this.errors.email = 'Email invalide';
    }
    
    if (!this.formData.company.trim()) {
      this.errors.company = 'Le nom de l\'entreprise est requis';
    }

    if (!this.formData.phone.trim()) {
      this.errors.phone = 'Le téléphone est requis';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async onSubmit(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // Envoyer via EmailJS
      await this.emailService.sendDemoRequest(this.formData);

      console.log('Demande de démo envoyée:', this.formData);
      this.isSubmitted = true;

      // Reset form after success
      setTimeout(() => {
        this.resetForm();
      }, 4000);

    } catch (error) {
      console.error('Erreur lors de l\'envoi:', error);
      // Vous pouvez ajouter une gestion d'erreur ici
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.formData = {
      name: '',
      email: '',
      company: '',
      phone: '',
      message: ''
    };
    this.isSubmitted = false;
    this.errors = {};
  }
}
