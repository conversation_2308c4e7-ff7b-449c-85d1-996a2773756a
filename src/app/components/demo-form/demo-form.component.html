<!-- Demo Form Section -->
<section id="demo" class="relative py-24 bg-gradient-to-br from-slate-50 via-white to-brand-50/20 overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-500/3 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-500/3 rounded-full blur-3xl"></div>
  </div>

  <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16 animate-on-scroll">
      <div class="inline-flex items-center justify-center p-2 bg-brand-100 rounded-full mb-6">
        <div class="flex items-center space-x-1">
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.2s;"></div>
          <div class="w-2 h-2 bg-brand-500 rounded-full animate-ping" style="animation-delay: 0.4s;"></div>
        </div>
      </div>
      <h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
        Demander une démo
      </h2>
      <p class="text-xl text-slate-600 max-w-2xl mx-auto">
        Découvrez comment Voyadem peut transformer votre agence de voyage en quelques minutes
      </p>
    </div>

    <!-- Form Container -->
    <div class="animate-on-scroll">
      <div class="relative max-w-2xl mx-auto">
        <!-- Glassmorphism background -->
        <div class="absolute inset-0 bg-white/60 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50"></div>
        
        <div class="relative p-8 md:p-12">
          <!-- Success State -->
          <div *ngIf="isSubmitted" class="text-center py-12">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-slate-900 mb-4">Demande envoyée !</h3>
            <p class="text-slate-600 mb-8">Nous vous contacterons dans les 24h pour planifier votre démo personnalisée.</p>
            <button (click)="resetForm()" 
                    class="inline-flex items-center px-6 py-3 text-base font-medium text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors">
              Nouvelle demande
            </button>
          </div>

          <!-- Form -->
          <form *ngIf="!isSubmitted" (ngSubmit)="onSubmit()" class="space-y-6">
            <!-- Name & Email Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Name Field -->
              <div class="form-group">
                <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                  Nom complet *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  [(ngModel)]="formData.name"
                  [class]="'form-input ' + (errors.name ? 'error' : '')"
                  placeholder="Votre nom"
                  required>
                <div *ngIf="errors.name" class="error-message">{{ errors.name }}</div>
              </div>

              <!-- Email Field -->
              <div class="form-group">
                <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                  Email professionnel *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  [(ngModel)]="formData.email"
                  [class]="'form-input ' + (errors.email ? 'error' : '')"
                  placeholder="<EMAIL>"
                  required>
                <div *ngIf="errors.email" class="error-message">{{ errors.email }}</div>
              </div>
            </div>

            <!-- Company & Phone Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Company Field -->
              <div class="form-group">
                <label for="company" class="block text-sm font-medium text-slate-700 mb-2">
                  Nom de l'agence *
                </label>
                <input
                  type="text"
                  id="company"
                  name="company"
                  [(ngModel)]="formData.company"
                  [class]="'form-input ' + (errors.company ? 'error' : '')"
                  placeholder="Nom de votre agence"
                  required>
                <div *ngIf="errors.company" class="error-message">{{ errors.company }}</div>
              </div>

              <!-- Phone Field -->
              <div class="form-group">
                <label for="phone" class="block text-sm font-medium text-slate-700 mb-2">
                  Téléphone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  [(ngModel)]="formData.phone"
                  class="form-input"
                  placeholder="+33 1 23 45 67 89">
              </div>
            </div>

            <!-- Message Field -->
            <div class="form-group">
              <label for="message" class="block text-sm font-medium text-slate-700 mb-2">
                Message (optionnel)
              </label>
              <textarea
                id="message"
                name="message"
                [(ngModel)]="formData.message"
                rows="4"
                class="form-input resize-none"
                placeholder="Parlez-nous de vos besoins spécifiques..."></textarea>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <button
                type="submit"
                [disabled]="isSubmitting"
                class="w-full group relative inline-flex items-center justify-center px-8 py-4 text-base font-medium text-slate-900 bg-white rounded-xl shadow-sm hover:shadow-md border border-slate-200 hover:border-slate-300 transition-all duration-300 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden">

                <!-- Gradient overlay on hover -->
                <span class="absolute inset-0 bg-gradient-to-r from-brand-500 to-brand-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>

                <span class="relative flex items-center font-semibold text-slate-900 group-hover:text-slate-900 transition-colors duration-300">
                  <span *ngIf="!isSubmitting">Demander ma démo gratuite</span>
                  <span *ngIf="isSubmitting" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Envoi en cours...
                  </span>
                  
                  <svg *ngIf="!isSubmitting" class="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  </svg>
                </span>
              </button>
            </div>

            <!-- Privacy Notice -->
            <p class="text-xs text-slate-500 text-center">
              En soumettant ce formulaire, vous acceptez d'être contacté par notre équipe. 
              Vos données sont protégées et ne seront jamais partagées.
            </p>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
