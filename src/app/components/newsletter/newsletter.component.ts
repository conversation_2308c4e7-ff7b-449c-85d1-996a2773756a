import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EmailService } from '../../services/email.service';

@Component({
  selector: 'app-newsletter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './newsletter.component.html',
  styleUrls: ['./newsletter.component.css']
})
export class NewsletterComponent {
  email = '';
  isSubmitting = false;
  isSubmitted = false;
  errors: any = {};

  constructor(private emailService: EmailService) {}

  validateForm(): boolean {
    this.errors = {};
    
    if (!this.email.trim()) {
      this.errors.email = 'L\'email est requis';
    } else if (!this.isValidEmail(this.email)) {
      this.errors.email = 'Email invalide';
    }

    return Object.keys(this.errors).length === 0;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async onSubmit(): Promise<void> {
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // Envoyer via EmailJS
      await this.emailService.sendNewsletterSubscription(this.email);
      
      console.log('Inscription newsletter envoyée:', this.email);
      this.isSubmitted = true;
      
      // Reset form after success
      setTimeout(() => {
        this.resetForm();
      }, 3000);
      
    } catch (error) {
      console.error('Erreur lors de l\'inscription:', error);
      // Vous pouvez ajouter une gestion d'erreur ici
    } finally {
      this.isSubmitting = false;
    }
  }

  resetForm(): void {
    this.email = '';
    this.isSubmitted = false;
    this.errors = {};
  }
}
