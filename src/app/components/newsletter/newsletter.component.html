<!-- Newsletter Component -->
<div class="newsletter-container">
  <h3 class="text-sm font-semibold text-slate-900 tracking-wider uppercase">Newsletter</h3>
  
  <!-- Success State -->
  <div *ngIf="isSubmitted" class="mt-4">
    <div class="flex items-center p-3 bg-brand-50 rounded-lg border border-brand-200">
      <svg class="w-5 h-5 text-brand-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
      </svg>
      <span class="text-sm text-brand-700 font-medium">Inscription réussie !</span>
    </div>
  </div>

  <!-- Form -->
  <div *ngIf="!isSubmitted">
    <p class="mt-4 text-base text-slate-500">
      Les dernières nouvelles, articles et ressources, envoyés chaque mois.
    </p>
    <form (ngSubmit)="onSubmit()" class="mt-4">
      <div class="flex flex-col space-y-3">
        <!-- Layout adaptatif avec breakpoint plus élevé -->
        <div class="hidden lg:flex">
          <input
            type="email"
            name="email"
            [(ngModel)]="email"
            [class]="'flex-1 px-3 py-2 text-sm border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 ' + (errors.email ? 'border-red-300' : 'border-slate-300')"
            placeholder="Votre email"
            required>
          <button
            type="submit"
            [disabled]="isSubmitting"
            class="flex-shrink-0 bg-brand-500 text-slate-900 rounded-r-lg px-3 py-2 text-sm font-medium hover:bg-brand-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <span *ngIf="!isSubmitting">OK</span>
            <span *ngIf="isSubmitting" class="flex items-center">
              <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
          </button>
        </div>

        <!-- Layout empilé pour tablette et mobile -->
        <div class="lg:hidden space-y-2">
          <input
            type="email"
            name="email"
            [(ngModel)]="email"
            [class]="'w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 ' + (errors.email ? 'border-red-300' : 'border-slate-300')"
            placeholder="Votre email"
            required>
          <button
            type="submit"
            [disabled]="isSubmitting"
            class="w-full bg-brand-500 text-slate-900 rounded-lg px-4 py-2 text-sm font-medium hover:bg-brand-400 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <span *ngIf="!isSubmitting">S'abonner</span>
            <span *ngIf="isSubmitting" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              ...
            </span>
          </button>
        </div>

        <div *ngIf="errors.email" class="text-xs text-red-600 ml-1">{{ errors.email }}</div>
      </div>
    </form>
  </div>
</div>
