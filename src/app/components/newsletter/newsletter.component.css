/* Newsletter component styles */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Input focus styles */
input:focus {
  transform: translateY(-1px);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  button {
    min-height: 44px; /* Better touch target */
  }
}

/* Ensure button text is always visible */
button {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mobile button full width */
@media (max-width: 639px) {
  button {
    white-space: normal;
  }
}
