/* Newsletter component styles */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Input focus styles */
input:focus {
  transform: translateY(-1px);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  button {
    min-height: 44px; /* Better touch target */
  }
}

/* Ensure button text is always visible */
button {
  white-space: nowrap;
  overflow: visible;
}

/* Specific fix for tablet range (764px - 1160px) */
@media (min-width: 640px) and (max-width: 1023px) {
  .newsletter-container {
    min-width: 0; /* Allow shrinking */
  }

  button {
    white-space: normal;
    min-width: auto;
    padding-left: 12px;
    padding-right: 12px;
  }
}

/* Large screens - horizontal layout */
@media (min-width: 1024px) {
  button {
    white-space: nowrap;
    min-width: 60px;
  }
}
