import {Component} from '@angular/core';
import {HeaderComponent} from './components/header/header.component';
import {HeroComponent} from './components/hero/hero.component';
import {SocialProofComponent} from './components/social-proof/social-proof.component';
import {FeaturesComponent} from './components/features/features.component';
import {PricingComponent} from './components/pricing/pricing.component';
import {TestimonialsComponent} from './components/testimonials/testimonials.component';
import {FooterComponent} from './components/footer/footer.component';

@Component({
  selector: 'app-root',
  imports: [HeaderComponent, HeroComponent, SocialProofComponent, FeaturesComponent, PricingComponent, TestimonialsComponent, FooterComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
  host: {ngSkipHydration: 'true'}
})
export class AppComponent {

}
