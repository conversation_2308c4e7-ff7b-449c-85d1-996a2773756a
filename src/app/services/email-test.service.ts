import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class EmailTestService {

  constructor() {}

  testConfiguration(): void {
    console.log('🔧 Configuration EmailJS:');
    console.log('Service ID:', environment.emailjs.serviceId);
    console.log('Public Key:', environment.emailjs.publicKey);
    console.log('Template Demo:', environment.emailjs.templates.demo);
    console.log('Template Contact:', environment.emailjs.templates.contact);
    console.log('✅ Configuration chargée avec succès!');
  }
}
