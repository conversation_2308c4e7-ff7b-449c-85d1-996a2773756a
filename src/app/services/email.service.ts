import { Injectable } from '@angular/core';
import emailjs from '@emailjs/browser';

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  private serviceId = 'YOUR_SERVICE_ID'; // À remplacer par votre Service ID
  private publicKey = 'YOUR_PUBLIC_KEY'; // À remplacer par votre Public Key

  // Template IDs
  private demoTemplateId = 'template_demo'; // Template pour demande de démo
  private contactTemplateId = 'template_contact'; // Template pour contact
  private newsletterTemplateId = 'template_newsletter'; // Template pour newsletter

  constructor() {
    // Initialiser EmailJS avec votre public key
    emailjs.init(this.publicKey);
  }

  async sendDemoRequest(formData: any): Promise<any> {
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      company: formData.company,
      phone: formData.phone || 'Non renseigné',
      message: formData.message || 'Demande de démo',
      form_type: 'demo',
      to_name: '<PERSON>qui<PERSON> Voyadem',
      reply_to: formData.email
    };

    try {
      const response = await emailjs.send(
        this.serviceId,
        this.demoTemplateId,
        templateParams
      );
      console.log('Demo email sent successfully:', response);
      return response;
    } catch (error) {
      console.error('Error sending demo email:', error);
      throw error;
    }
  }

  async sendContactMessage(formData: any): Promise<any> {
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      company: formData.company,
      phone: formData.phone || 'Non renseigné',
      subject: this.getSubjectLabel(formData.subject),
      message: formData.message,
      form_type: 'contact',
      newsletter: 'non',
      to_name: 'Équipe Voyadem',
      reply_to: formData.email
    };

    try {
      const response = await emailjs.send(
        this.serviceId,
        this.contactTemplateId,
        templateParams
      );
      console.log('Contact email sent successfully:', response);
      return response;
    } catch (error) {
      console.error('Error sending contact email:', error);
      throw error;
    }
  }

  async sendNewsletterSubscription(email: string): Promise<any> {
    const templateParams = {
      from_email: email,
      form_type: 'newsletter',
      newsletter: 'oui',
      to_name: 'Équipe Voyadem',
      reply_to: email,
      message: 'Nouvelle inscription à la newsletter'
    };

    try {
      const response = await emailjs.send(
        this.serviceId,
        this.contactTemplateId, // Utilise le même template que contact
        templateParams
      );
      console.log('Newsletter subscription sent successfully:', response);
      return response;
    } catch (error) {
      console.error('Error sending newsletter subscription:', error);
      throw error;
    }
  }

  private getSubjectLabel(subject: string): string {
    const subjects: { [key: string]: string } = {
      'commercial': 'Demande commerciale',
      'support': 'Support technique',
      'partnership': 'Partenariat',
      'other': 'Autre demande'
    };
    return subjects[subject] || 'Autre demande';
  }
}
