import { Injectable } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class SeoService {
  constructor(
    private meta: Meta,
    private title: Title
  ) {}

  updateMetaTags(config: {
    title?: string;
    description?: string;
    image?: string;
    url?: string;
  }) {
    const defaultConfig = {
      title: 'Voyadem | La Solution pour Agences de Voyages',
      description: 'Voyadem révolutionne la gestion des agences de voyages avec une solution tout-en-un. Simplifiez vos opérations quotidiennes et concentrez-vous sur l\'essentiel.',
      image: 'https://voyadem.com/assets/images/airplane_voyadem.png',
      url: 'https://voyadem.ma'
    };

    const seoConfig = { ...defaultConfig, ...config };

    // Basic Meta Tags
    this.title.setTitle(seoConfig.title);
    this.meta.updateTag({ name: 'description', content: seoConfig.description });

    // OpenGraph Meta Tags
    this.meta.updateTag({ property: 'og:title', content: seoConfig.title });
    this.meta.updateTag({ property: 'og:description', content: seoConfig.description });
    this.meta.updateTag({ property: 'og:image', content: seoConfig.image });
    this.meta.updateTag({ property: 'og:url', content: seoConfig.url });
    this.meta.updateTag({ property: 'og:type', content: 'website' });

    // Twitter Card Meta Tags
    this.meta.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:title', content: seoConfig.title });
    this.meta.updateTag({ name: 'twitter:description', content: seoConfig.description });
    this.meta.updateTag({ name: 'twitter:image', content: seoConfig.image });
  }
}
